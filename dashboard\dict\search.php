<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
// ========== search.php ==========

// --- CONFIGURACIÓN DE LA BASE DE DATOS ---
$host = 'srv944.hstgr.io';
$port = 3306;
$dbname = 'u636704306_Dictionaries';
$user = 'u636704306_Kevs';
$pass = '5@8W>|NRe';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=$charset";

// --- CONFIGURACIÓN DE LA CONEXIÓN (PDO) ---
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (\PDOException $e) {
    // En un entorno de producción, registrar el error en lugar de mostrarlo
    http_response_code(500);
    echo json_encode(['error' => 'Error de conexión a la base de datos.']);
    echo "Error de conexión SQL: " . $e->getMessage();
    // Log a más detalle: throw new \PDOException($e->getMessage(), (int)$e->getCode());
    exit;
}

// --- RESPUESTA JSON ---
header('Content-Type: application/json; charset=utf-8');

// --- OBTENER PARÁMETROS DE LA SOLICITUD ---
$query = isset($_GET['query']) ? trim($_GET['query']) : '';
$searchAsJapanese = isset($_GET['searchAsJapanese']) ? filter_var($_GET['searchAsJapanese'], FILTER_VALIDATE_BOOLEAN) : false;

if (empty($query)) {
    echo json_encode(['vocab' => [], 'kanji' => []]);
    exit;
}

// --- INICIALIZAR RESULTADOS ---
$response = [
    'vocab' => [],
    'kanji' => []
];

// --- PREPARAR TÉRMINO DE BÚSQUEDA PARA LIKE ---
$likeQuery = '%' . $query . '%';

// ========== BÚSQUEDA DE VOCABULARIO (registros_full) ==========
if ($searchAsJapanese) {
    // Buscar en columnas de kanji o kana
    $sqlVocab = "SELECT * FROM registros_full WHERE KJ_tx LIKE ? OR KN_tx LIKE ? LIMIT 200";
    $stmtVocab = $pdo->prepare($sqlVocab);
    $stmtVocab->execute([$likeQuery, $likeQuery]);
} else {
    // Buscar en columnas de significados en español o inglés
    $sqlVocab = "SELECT * FROM registros_full WHERE SE_GL_2_tx LIKE ? OR SE_GL_1_tx LIKE ? LIMIT 200";
    $stmtVocab = $pdo->prepare($sqlVocab);
    $stmtVocab->execute([$likeQuery, $likeQuery]);
}
$response['vocab'] = $stmtVocab->fetchAll();


// ========== BÚSQUEDA DE KANJI (kanji_dict) ==========
if ($searchAsJapanese) {
    // Si la búsqueda es un solo caracter, buscar coincidencia exacta en el kanji
    if (mb_strlen($query, 'UTF-8') === 1) {
         $sqlKanji = "SELECT * FROM kanji_dict WHERE li_tx = ? OR rM_g_rd_on_vl LIKE ? OR rM_g_rd_kun_vl LIKE ? LIMIT 100";
         $stmtKanji = $pdo->prepare($sqlKanji);
         $stmtKanji->execute([$query, $likeQuery, $likeQuery]);
    } else {
        // Buscar en lecturas on/kun para búsquedas más largas
        $sqlKanji = "SELECT * FROM kanji_dict WHERE rM_g_rd_on_vl LIKE ? OR rM_g_rd_kun_vl LIKE ? LIMIT 100";
        $stmtKanji = $pdo->prepare($sqlKanji);
        $stmtKanji->execute([$likeQuery, $likeQuery]);
    }
} else {
    // Buscar en significados en español o inglés
    $sqlKanji = "SELECT * FROM kanji_dict WHERE mn_ln_es_vl LIKE ? OR mn_ln_en_vl LIKE ? LIMIT 100";
    $stmtKanji = $pdo->prepare($sqlKanji);
    $stmtKanji->execute([$likeQuery, $likeQuery]);
}
$response['kanji'] = $stmtKanji->fetchAll();


// --- DEVOLVER LA RESPUESTA JSON ---
echo json_encode($response);
?>